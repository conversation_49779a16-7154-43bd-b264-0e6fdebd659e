<?xml version="1.0" encoding="UTF-8"?>
<configs>
    <!-- Přidání Super Auger do progression systému -->
    <append xpath="/progression/perks/perk[@name='perkMiner']/level[@level='5']">
        <reward type="Recipe" id="meleeToolSuperAugerSchematic"/>
    </append>
    
    <!-- Alternativně přidání do Construction Tools -->
    <append xpath="/progression/perks/perk[@name='perkAdvancedEngineering']/level[@level='4']">
        <reward type="Recipe" id="meleeToolSuperAugerSchematic"/>
    </append>
    
    <!-- Nový perk specificky pro Super Auger -->
    <append xpath="/progression/perks">
        <perk name="perkSuperAuger" max_level="3" base_skill_point_cost="1" name_key="perkSuperAugerName" desc_key="perkSuperAugerDesc" icon="ui_game_symbol_hammer">
            <level level="1" name_key="perkSuperAugerRank1" desc_key="perkSuperAugerRank1Desc" skill_point_cost="1">
                <effect_group>
                    <passive_effect name="BlockDamage" operation="perc_add" value="0.2" tags="meleeToolSuperAuger"/>
                    <passive_effect name="HarvestCount" operation="perc_add" value="0.1" tags="meleeToolSuperAuger"/>
                </effect_group>
            </level>
            <level level="2" name_key="perkSuperAugerRank2" desc_key="perkSuperAugerRank2Desc" skill_point_cost="1">
                <effect_group>
                    <passive_effect name="BlockDamage" operation="perc_add" value="0.4" tags="meleeToolSuperAuger"/>
                    <passive_effect name="HarvestCount" operation="perc_add" value="0.2" tags="meleeToolSuperAuger"/>
                    <passive_effect name="DegradationPerUse" operation="perc_add" value="-0.1" tags="meleeToolSuperAuger"/>
                </effect_group>
            </level>
            <level level="3" name_key="perkSuperAugerRank3" desc_key="perkSuperAugerRank3Desc" skill_point_cost="2">
                <effect_group>
                    <passive_effect name="BlockDamage" operation="perc_add" value="0.6" tags="meleeToolSuperAuger"/>
                    <passive_effect name="HarvestCount" operation="perc_add" value="0.3" tags="meleeToolSuperAuger"/>
                    <passive_effect name="DegradationPerUse" operation="perc_add" value="-0.2" tags="meleeToolSuperAuger"/>
                    <passive_effect name="AttacksPerMinute" operation="perc_add" value="0.15" tags="meleeToolSuperAuger"/>
                </effect_group>
            </level>
        </perk>
    </append>
</configs>
